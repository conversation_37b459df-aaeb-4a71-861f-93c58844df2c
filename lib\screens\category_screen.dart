// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../data/categories_data.dart';

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final categories = CategoriesData.getCategoriesWithPremiumColors(
      themeProvider,
    );

    return Scaffold(
      appBar: Custom3DAppBar(
        title: 'Charm Shots',
        actions: const [], // No heart icon
      ),
      drawer: const AppDrawer(),
      backgroundColor: Colors.white, // White background
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: CustomScrollView(
          // Enhanced smooth scrolling configuration
          cacheExtent: 1500, // Increased cache for smoother scrolling
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          slivers: [
            // Top spacing
            const SliverToBoxAdapter(
              child: SizedBox(height: 20),
            ),

            // Premium category grid - optimized with performance enhancements
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final cat = categories[index];
                    return Container(
                      margin:
                          const EdgeInsets.only(bottom: 30), // Increased space
                      child: PremiumCategoryCard(
                        key: ValueKey('category_${cat['name']}'),
                        title: cat['name'] as String,
                        icon: cat['icon'] as IconData?,
                        iconAsset: cat['iconAsset'] as String?,
                        gradientColors: cat['gradientColors'] as List<Color>,
                        shadowColor: cat['shadowColor'] as Color,
                        onTap: () =>
                            _navigateToLines(context, cat['name'] as String),
                      ),
                    );
                  },
                  childCount: categories.length,
                  addAutomaticKeepAlives: false,
                  addRepaintBoundaries: false,
                  addSemanticIndexes: false,
                ),
              ),
            ),

            // Bottom padding
            const SliverToBoxAdapter(child: SizedBox(height: 30)),
          ],
        ),
      ),
    );
  }

  void _navigateToLines(BuildContext context, String categoryName) {
    Navigator.pushNamed(
      context,
      '/lines',
      arguments: {'category': categoryName, 'language': language},
    );
  }
}

class PremiumCategoryCard extends StatelessWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final List<Color> gradientColors;
  final Color shadowColor;
  final VoidCallback onTap;

  const PremiumCategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    required this.gradientColors,
    required this.shadowColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 85,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        boxShadow: [
          BoxShadow(
            color: shadowColor.withValues(alpha: 0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            HapticFeedback.selectionClick();
            onTap();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: iconAsset != null
                        ? SvgPicture.asset(
                            iconAsset!,
                            width: 28,
                            height: 28,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          )
                        : Icon(
                            icon ?? Icons.category_rounded,
                            color: Colors.white,
                            size: 28,
                          ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 0.5,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
