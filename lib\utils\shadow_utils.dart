// lib/utils/shadow_utils.dart
// Utility class for creating realistic 3D shadow effects

import 'package:flutter/material.dart';

class ShadowUtils {
  // Realistic 3D shadow presets for different elevation levels
  
  /// Creates a realistic 3D shadow effect for elevated surfaces
  /// [elevation] - Height of the surface (1-24)
  /// [isDarkMode] - Whether to use dark mode shadow colors
  /// [color] - Optional custom shadow color
  static List<BoxShadow> create3DShadow({
    required double elevation,
    required bool isDarkMode,
    Color? color,
  }) {
    // Clamp elevation to reasonable values
    elevation = elevation.clamp(1.0, 24.0);
    
    // Base shadow color
    final shadowColor = color ?? (isDarkMode ? Colors.black : Colors.black);
    
    // Calculate shadow properties based on elevation
    final primaryOpacity = isDarkMode 
        ? (0.3 + (elevation * 0.02)).clamp(0.3, 0.6)
        : (0.1 + (elevation * 0.01)).clamp(0.1, 0.3);
    
    final secondaryOpacity = isDarkMode
        ? (0.2 + (elevation * 0.015)).clamp(0.2, 0.4)
        : (0.05 + (elevation * 0.008)).clamp(0.05, 0.15);
    
    final ambientOpacity = isDarkMode
        ? (0.15 + (elevation * 0.01)).clamp(0.15, 0.25)
        : (0.03 + (elevation * 0.005)).clamp(0.03, 0.08);
    
    return [
      // Primary shadow (key light)
      BoxShadow(
        color: shadowColor.withValues(alpha: primaryOpacity),
        spreadRadius: 0,
        blurRadius: elevation * 1.5,
        offset: Offset(0, elevation * 0.5),
      ),
      // Secondary shadow (fill light)
      BoxShadow(
        color: shadowColor.withValues(alpha: secondaryOpacity),
        spreadRadius: 0,
        blurRadius: elevation * 3,
        offset: Offset(0, elevation * 1.2),
      ),
      // Ambient shadow (soft overall shadow)
      BoxShadow(
        color: shadowColor.withValues(alpha: ambientOpacity),
        spreadRadius: elevation * 0.1,
        blurRadius: elevation * 4,
        offset: Offset(0, elevation * 0.8),
      ),
    ];
  }
  
  /// Creates a floating card shadow effect
  static List<BoxShadow> createFloatingCardShadow({
    required bool isDarkMode,
    double elevation = 8.0,
    Color? color,
  }) {
    return create3DShadow(
      elevation: elevation,
      isDarkMode: isDarkMode,
      color: color,
    );
  }
  
  /// Creates an app bar shadow effect
  static List<BoxShadow> createAppBarShadow({
    required bool isDarkMode,
    double elevation = 4.0,
  }) {
    final shadowColor = isDarkMode ? Colors.black : Colors.black;
    
    return [
      // Primary shadow
      BoxShadow(
        color: shadowColor.withValues(alpha: isDarkMode ? 0.4 : 0.15),
        spreadRadius: 0,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
      // Secondary shadow for depth
      BoxShadow(
        color: shadowColor.withValues(alpha: isDarkMode ? 0.3 : 0.08),
        spreadRadius: 0,
        blurRadius: 16,
        offset: const Offset(0, 4),
      ),
      // Subtle ambient shadow
      BoxShadow(
        color: shadowColor.withValues(alpha: isDarkMode ? 0.2 : 0.04),
        spreadRadius: 1,
        blurRadius: 24,
        offset: const Offset(0, 6),
      ),
    ];
  }
  
  /// Creates a pressed/inset shadow effect
  static List<BoxShadow> createInsetShadow({
    required bool isDarkMode,
    double depth = 2.0,
  }) {
    final shadowColor = isDarkMode ? Colors.black : Colors.black;
    
    return [
      BoxShadow(
        color: shadowColor.withValues(alpha: isDarkMode ? 0.3 : 0.1),
        spreadRadius: 0,
        blurRadius: depth * 2,
        offset: Offset(0, -depth),
      ),
    ];
  }
  
  /// Creates a glowing effect shadow
  static List<BoxShadow> createGlowShadow({
    required Color glowColor,
    double intensity = 0.3,
    double radius = 12.0,
  }) {
    return [
      BoxShadow(
        color: glowColor.withValues(alpha: intensity),
        spreadRadius: 2,
        blurRadius: radius,
        offset: const Offset(0, 0),
      ),
      BoxShadow(
        color: glowColor.withValues(alpha: intensity * 0.5),
        spreadRadius: 4,
        blurRadius: radius * 2,
        offset: const Offset(0, 0),
      ),
    ];
  }
  
  /// Creates a realistic button shadow
  static List<BoxShadow> createButtonShadow({
    required bool isDarkMode,
    bool isPressed = false,
    Color? color,
  }) {
    if (isPressed) {
      return createInsetShadow(isDarkMode: isDarkMode, depth: 1.0);
    }
    
    return create3DShadow(
      elevation: 3.0,
      isDarkMode: isDarkMode,
      color: color,
    );
  }
  
  /// Creates a realistic drawer shadow
  static List<BoxShadow> createDrawerShadow({
    required bool isDarkMode,
  }) {
    final shadowColor = isDarkMode ? Colors.black : Colors.black;
    
    return [
      // Strong directional shadow
      BoxShadow(
        color: shadowColor.withValues(alpha: isDarkMode ? 0.5 : 0.2),
        spreadRadius: 0,
        blurRadius: 16,
        offset: const Offset(8, 0),
      ),
      // Soft ambient shadow
      BoxShadow(
        color: shadowColor.withValues(alpha: isDarkMode ? 0.3 : 0.1),
        spreadRadius: 0,
        blurRadius: 32,
        offset: const Offset(4, 0),
      ),
    ];
  }
}
