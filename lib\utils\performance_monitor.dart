// lib/utils/performance_monitor.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  int _frameDropCount = 0;
  int _totalFrames = 0;
  DateTime? _lastFrameTime;
  bool _isMonitoring = false;

  /// Start monitoring frame performance
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _frameDropCount = 0;
    _totalFrames = 0;
    _lastFrameTime = DateTime.now();

    if (kDebugMode) {
      debugPrint('🚀 Frame monitoring started');
    }

    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);
  }

  /// Stop monitoring frame performance
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    // Note: Persistent frame callbacks cannot be removed individually
    // They automatically stop when _isMonitoring is false

    if (kDebugMode) {
      final dropRate = _totalFrames > 0
          ? (_frameDropCount / _totalFrames * 100)
          : 0;
      debugPrint('📊 Performance monitoring stopped');
      debugPrint('   Total frames: $_totalFrames');
      debugPrint('   Frame drops: $_frameDropCount');
      debugPrint('   Drop rate: ${dropRate.toStringAsFixed(1)}%');
    }
  }

  void _onFrame(Duration timestamp) {
    if (!_isMonitoring) return;

    _totalFrames++;
    final now = DateTime.now();

    if (_lastFrameTime != null) {
      final frameDuration = now.difference(_lastFrameTime!).inMilliseconds;

      // Consider frame drop if frame takes longer than 16.67ms (60fps)
      if (frameDuration > 16.67) {
        _frameDropCount++;

        if (kDebugMode && frameDuration > 100) {
          debugPrint('⚠️ Severe frame drop: ${frameDuration}ms');
        }
      }
    }

    _lastFrameTime = now;
  }

  /// Get current performance stats
  Map<String, dynamic> getStats() {
    final dropRate = _totalFrames > 0
        ? (_frameDropCount / _totalFrames * 100)
        : 0;

    return {
      'isMonitoring': _isMonitoring,
      'totalFrames': _totalFrames,
      'frameDrops': _frameDropCount,
      'dropRate': dropRate,
      'performance': dropRate < 5
          ? 'Excellent'
          : dropRate < 15
          ? 'Good'
          : dropRate < 30
          ? 'Fair'
          : 'Poor',
    };
  }

  /// Reset monitoring stats
  void reset() {
    _frameDropCount = 0;
    _totalFrames = 0;
    _lastFrameTime = null;
  }
}
