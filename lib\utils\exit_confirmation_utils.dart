// lib/utils/exit_confirmation_utils.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ExitConfirmationUtils {
  /// Shows a confirmation dialog when user tries to exit the app
  /// Returns true if user confirms exit, false otherwise
  static Future<bool> showExitConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Row(
            children: [
              Icon(Icons.exit_to_app, color: Colors.orange),
              SizedBox(width: 10),
              Text('Exit App'),
            ],
          ),
          content: const Text(
            'Do you really want to exit Charm Shots?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'No',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Yes',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Handles the PopScope onPopInvokedWithResult callback
  /// Shows exit confirmation and exits app if confirmed
  static Future<void> handlePopInvoked(
    bool didPop,
    dynamic result,
    BuildContext context,
  ) async {
    if (didPop) return;

    final shouldExit = await showExitConfirmationDialog(context);
    if (shouldExit && context.mounted) {
      SystemNavigator.pop();
    }
  }

  /// Creates a PopScope widget with exit confirmation
  /// Wraps the child widget with exit confirmation functionality
  static Widget wrapWithExitConfirmation({
    required Widget child,
    required BuildContext context,
  }) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        await handlePopInvoked(didPop, result, context);
      },
      child: child,
    );
  }
}
