// lib/providers/favorites_provider.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class FavoritePost {
  final String id;
  final String text;
  final String backgroundImage;
  final DateTime addedAt;

  FavoritePost({
    required this.id,
    required this.text,
    required this.backgroundImage,
    required this.addedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'backgroundImage': backgroundImage,
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory FavoritePost.fromJson(Map<String, dynamic> json) {
    return FavoritePost(
      id: json['id'],
      text: json['text'],
      backgroundImage: json['backgroundImage'],
      addedAt: DateTime.parse(json['addedAt']),
    );
  }
}

class FavoritesProvider extends ChangeNotifier {
  final List<FavoritePost> _favorites = [];
  bool _tapSoundEnabled = true;
  static const String _favoritesKey = 'favorites_list';
  static const String _tapSoundKey = 'tap_sound_enabled';

  List<FavoritePost> get favorites => List.unmodifiable(_favorites);
  bool get tapSoundEnabled => _tapSoundEnabled;
  int get favoritesCount => _favorites.length;

  // Initialize and load saved data
  Future<void> initialize() async {
    await _loadFavorites();
    await _loadTapSoundSetting();
  }

  // Load favorites from SharedPreferences
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];

      _favorites.clear();
      for (String favoriteJson in favoritesJson) {
        try {
          final Map<String, dynamic> favoriteMap = jsonDecode(favoriteJson);
          _favorites.add(FavoritePost.fromJson(favoriteMap));
        } catch (e) {
          debugPrint('Error parsing favorite: $e');
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading favorites: $e');
    }
  }

  // Save favorites to SharedPreferences
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = _favorites
          .map((favorite) => jsonEncode(favorite.toJson()))
          .toList();
      await prefs.setStringList(_favoritesKey, favoritesJson);
    } catch (e) {
      debugPrint('Error saving favorites: $e');
    }
  }

  // Load tap sound setting from SharedPreferences
  Future<void> _loadTapSoundSetting() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _tapSoundEnabled = prefs.getBool(_tapSoundKey) ?? true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading tap sound setting: $e');
    }
  }

  // Save tap sound setting to SharedPreferences
  Future<void> _saveTapSoundSetting() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_tapSoundKey, _tapSoundEnabled);
    } catch (e) {
      debugPrint('Error saving tap sound setting: $e');
    }
  }

  void setTapSoundEnabled(bool enabled) {
    _tapSoundEnabled = enabled;
    _saveTapSoundSetting(); // Save immediately
    notifyListeners();
  }

  bool isFavorite(String postId) {
    return _favorites.any((post) => post.id == postId);
  }

  void addToFavorites(String postId, String text, String backgroundImage) {
    // Check if already in favorites
    if (isFavorite(postId)) {
      return;
    }

    final favoritePost = FavoritePost(
      id: postId,
      text: text,
      backgroundImage: backgroundImage,
      addedAt: DateTime.now(),
    );

    _favorites.insert(0, favoritePost); // Add to beginning for recent first
    _saveFavorites(); // Save to persistent storage
    _playTapSound();
    notifyListeners();
  }

  void removeFromFavorites(String postId) {
    _favorites.removeWhere((post) => post.id == postId);
    _saveFavorites(); // Save to persistent storage
    _playTapSound();
    notifyListeners();
  }

  void toggleFavorite(String postId, String text, String backgroundImage) {
    if (isFavorite(postId)) {
      removeFromFavorites(postId);
    } else {
      addToFavorites(postId, text, backgroundImage);
    }
  }

  void clearAllFavorites() {
    _favorites.clear();
    _saveFavorites(); // Save to persistent storage
    notifyListeners();
  }

  List<FavoritePost> getFavoritesByDate() {
    final sortedFavorites = List<FavoritePost>.from(_favorites);
    sortedFavorites.sort((a, b) => b.addedAt.compareTo(a.addedAt));
    return sortedFavorites;
  }

  void _playTapSound() {
    if (_tapSoundEnabled) {
      try {
        SystemSound.play(SystemSoundType.click);
      } catch (e) {
        // Ignore sound errors
        debugPrint('Sound play error: $e');
      }
    }
  }

  // Get favorites for a specific time period
  List<FavoritePost> getFavoritesFromLastDays(int days) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return _favorites
        .where((post) => post.addedAt.isAfter(cutoffDate))
        .toList();
  }

  // Search favorites by text
  List<FavoritePost> searchFavorites(String query) {
    if (query.isEmpty) return favorites;

    return _favorites
        .where((post) => post.text.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }
}
