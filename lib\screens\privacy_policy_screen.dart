// lib/screens/privacy_policy_screen.dart
// Privacy Policy screen for Play Store compliance

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/custom_app_bar.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor: Colors.white, // Always white background
          appBar: Custom3DAppBar(
            title: 'Privacy Policy',
            elevation: 12.0, // Enhanced 3D effect
            backgroundColor: Colors.white,
            foregroundColor: Colors.black87,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white,
                  Colors.grey[50]!,
                ], // Always white gradient
              ),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white, // Always white
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: themeProvider.getCardShadow(
                    elevation: 12.0,
                  ), // Enhanced 3D shadow
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Information We Collect',
                      'Charm Shots does not collect, store, or transmit any personal information. The app works entirely offline and does not require any personal data to function.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Data Usage',
                      'All pickup lines and content are stored locally on your device. No data is sent to external servers or third parties.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Permissions',
                      'The app may request minimal permissions for basic functionality such as storage access for sharing features. These permissions are used solely for app functionality.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Third-Party Services',
                      'Charm Shots does not integrate with any third-party analytics, advertising, or data collection services.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Children\'s Privacy',
                      'Our app is designed to be family-friendly and does not knowingly collect information from children under 13.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Changes to Privacy Policy',
                      'We may update this privacy policy from time to time. Any changes will be reflected in the app update.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Contact Us',
                      'If you have any questions about this privacy policy, please contact us through the app store.',
                      themeProvider,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Last updated: ${DateTime.now().toString().split(' ')[0]}',
                      style: TextStyle(
                        fontSize: 12,
                        color: themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSection(
    String title,
    String content,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87, // Always dark text on white background
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: Colors.grey[700], // Always dark grey text
            ),
          ),
        ],
      ),
    );
  }
}
