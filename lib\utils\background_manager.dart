// lib/utils/background_manager.dart
// Centralized background management for pickup lines app
// Supports random backgrounds and easy scaling to more images

import 'dart:math';

class BackgroundManager {
  static final BackgroundManager _instance = BackgroundManager._internal();
  factory BackgroundManager() => _instance;
  BackgroundManager._internal();

  static final Random _random = Random();

  // Available backgrounds with their actual file extensions
  static const Map<int, String> _availableBackgrounds = {
    0: 'png',
    1: 'png',
    2: 'png',
    3: 'png',
    4: 'png',
    5: 'png',
    6: 'png',
    7: 'png',
    8: 'png',
    9: 'png',
    10: 'png',
  };

  /// Get all available background image paths
  static List<String> getAllBackgrounds() {
    return _availableBackgrounds.entries
        .map((entry) => 'assets/images/backgrounds/${entry.key}.${entry.value}')
        .toList();
  }

  /// Get a random background image path
  static String getRandomBackground() {
    final randomIndex = _random.nextInt(_availableBackgrounds.length);
    final backgroundNumber = _availableBackgrounds[randomIndex];
    return 'assets/images/$backgroundNumber.png';
  }

  /// Get a random background different from the current one
  static String getRandomBackgroundExcluding(String currentBackground) {
    if (_availableBackgrounds.length <= 1) {
      return getAllBackgrounds().first;
    }

    String newBackground;
    do {
      newBackground = getRandomBackground();
    } while (newBackground == currentBackground);

    return newBackground;
  }

  /// Get background by index (for cycling through backgrounds)
  static String getBackgroundByIndex(int index) {
    final safeIndex = index % _availableBackgrounds.length;
    final backgroundNumber = _availableBackgrounds[safeIndex];
    return 'assets/images/$backgroundNumber.png';
  }

  /// Get next background in sequence
  static String getNextBackground(String currentBackground) {
    final allBackgrounds = getAllBackgrounds();
    final currentIndex = allBackgrounds.indexOf(currentBackground);

    if (currentIndex == -1) {
      return allBackgrounds.first;
    }

    final nextIndex = (currentIndex + 1) % allBackgrounds.length;
    return allBackgrounds[nextIndex];
  }

  /// Get total number of available backgrounds
  static int getTotalBackgroundCount() {
    return _availableBackgrounds.length;
  }

  /// Check if a background exists
  static bool backgroundExists(String backgroundPath) {
    return getAllBackgrounds().contains(backgroundPath);
  }

  /// Get random backgrounds for multiple posts (ensures variety)
  static List<String> getRandomBackgroundsForPosts(int postCount) {
    final List<String> backgrounds = [];
    final allBackgrounds = getAllBackgrounds();

    for (int i = 0; i < postCount; i++) {
      // Try to avoid consecutive duplicates
      String newBackground;
      do {
        newBackground = getRandomBackground();
      } while (backgrounds.isNotEmpty &&
          backgrounds.last == newBackground &&
          allBackgrounds.length > 1);

      backgrounds.add(newBackground);
    }

    return backgrounds;
  }

  /// Get a seeded random background (consistent for same seed)
  static String getSeededRandomBackground(int seed) {
    final seededRandom = Random(seed);
    final randomIndex = seededRandom.nextInt(_availableBackgrounds.length);
    final backgroundNumber = _availableBackgrounds[randomIndex];
    return 'assets/images/$backgroundNumber.png';
  }

  /// Get background based on post content hash (consistent per post)
  static String getBackgroundForPost(String postContent) {
    final hash = postContent.hashCode.abs();
    final index = hash % _availableBackgrounds.length;
    final backgroundNumber = _availableBackgrounds[index];
    return 'assets/images/$backgroundNumber.png';
  }

  /// Future expansion helper - add new backgrounds easily
  /// When you add backgrounds 11-20, just call this method
  static void addNewBackgrounds(List<int> newBackgroundNumbers) {
    // This is a placeholder for future expansion
    // In the future, you can make _availableBackgrounds non-const
    // and add new backgrounds dynamically

    // Example usage when you add 11.png to 20.png:
    // BackgroundManager.addNewBackgrounds([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);
  }

  /// Get background info for debugging
  static Map<String, dynamic> getBackgroundInfo() {
    return {
      'totalBackgrounds': getTotalBackgroundCount(),
      'availableNumbers': _availableBackgrounds,
      'allPaths': getAllBackgrounds(),
    };
  }
}
