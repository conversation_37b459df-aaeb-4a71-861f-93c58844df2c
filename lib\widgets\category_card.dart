// lib/widgets/category_card.dart
import 'package:flutter/material.dart';
import 'svg_icon.dart';

class CategoryCard extends StatelessWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final Color? color; // Made optional for backward compatibility
  final List<Color>? gradientColors; // New premium gradient colors
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    this.color,
    this.gradientColors,
    required this.onTap,
  });

  // Cache gradient colors to avoid recalculation
  static final Map<String, List<Color>> _gradientCache = {};
  static final Map<String, Color> _shadowCache = {};

  Widget _buildIcon() {
    // Check if we have an asset path and if it's an SVG
    if (iconAsset != null) {
      if (iconAsset!.toLowerCase().endsWith('.svg')) {
        // Use SvgIcon for SVG files with optimized settings
        return SvgIcon(
          assetPath: iconAsset!,
          width: 32,
          height: 32,
          color: Colors.white,
          fallbackIcon: _getFallbackIcon(),
        );
      } else {
        // Use optimized Image.asset for PNG/other image files
        return Image.asset(
          iconAsset!,
          width: 32,
          height: 32,
          fit: BoxFit.contain,
          color: Colors.white,
          colorBlendMode: BlendMode.srcIn,
          cacheWidth: 32,
          cacheHeight: 32,
          filterQuality: FilterQuality.low, // Fastest rendering
          gaplessPlayback: true, // Prevent flicker
          errorBuilder: (context, error, stackTrace) {
            return Icon(_getFallbackIcon(), size: 32, color: Colors.white);
          },
        );
      }
    } else {
      return Icon(icon ?? Icons.category, size: 32, color: Colors.white);
    }
  }

  IconData _getFallbackIcon() {
    switch (title.toLowerCase()) {
      case 'bold':
        return Icons.flash_on;
      case 'bad':
        return Icons.thumb_down;
      case 'cute':
        return Icons.favorite;
      case 'clever':
        return Icons.lightbulb;
      case 'genius':
        return Icons.psychology;
      case 'dirty':
        return Icons.whatshot;
      case 'flirty':
        return Icons.face;
      case 'hookup':
        return Icons.nightlife;
      case 'romantic':
        return Icons.favorite_border;
      case 'funny':
        return Icons.emoji_emotions;
      case 'nerd':
        return Icons.science;
      case 'food':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Pre-calculate colors to avoid Consumer overhead
    final String cacheKey = title;

    // Use cached gradient colors or calculate once
    final List<Color> cardColors = _gradientCache[cacheKey] ??=
        gradientColors ??
        [
          color ?? Colors.purple,
          (color ?? Colors.purple).withValues(alpha: 0.8),
          color ?? Colors.purple,
        ];

    final Color shadowColor = _shadowCache[cacheKey] ??=
        gradientColors?.first ?? color ?? Colors.purple;

    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 16,
      ), // Reduced vertical spacing to match screenshot
      height: 80, // Rectangle height to match screenshot proportions
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          16,
        ), // Rounded corners like screenshot
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: cardColors,
        ),
        boxShadow: [
          // Simplified shadow for better performance
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.12),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          // Single colored shadow
          BoxShadow(
            color: shadowColor.withValues(alpha: 0.2),
            spreadRadius: 0,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16), // Match container radius
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 16,
            ), // Optimized padding for rectangle shape
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 28, // Optimized for rectangle height
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(1, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  width: 48, // Optimized for rectangle height
                  height: 48, // Optimized for rectangle height
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.25),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: const [
                      // Simplified shadow for icon container
                      BoxShadow(
                        color: Colors.black12,
                        spreadRadius: 0,
                        blurRadius: 3,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Center(child: _buildIcon()),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
