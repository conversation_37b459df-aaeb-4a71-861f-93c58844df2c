// lib/widgets/enhanced_edit_dialog.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import 'rich_text_editor.dart';

class EnhancedEditDialog extends StatefulWidget {
  final String initialText;
  final Function(String, TextStyle) onSave;

  const EnhancedEditDialog({
    super.key,
    required this.initialText,
    required this.onSave,
  });

  @override
  State<EnhancedEditDialog> createState() => _EnhancedEditDialogState();
}

class _EnhancedEditDialogState extends State<EnhancedEditDialog> {
  String _currentText = '';
  TextStyle _currentStyle = const TextStyle(
    fontSize: 18,
    color: Colors.white,
    fontWeight: FontWeight.bold,
    height: 1.3,
  );

  @override
  void initState() {
    super.initState();
    _currentText = widget.initialText;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.all(16),
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.9,
            ),
            decoration: BoxDecoration(
              color: themeProvider.cardBackgroundColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: themeProvider.getCardShadow(elevation: 12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: themeProvider.surfaceColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: Colors.purple.shade600, size: 28),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Edit Quote',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: themeProvider.textColor,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.close,
                          color: themeProvider.subtitleColor,
                        ),
                      ),
                    ],
                  ),
                ),

                // Rich text editor
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(20),
                    child: RichTextEditor(
                      initialText: widget.initialText,
                      onTextChanged: (text) {
                        setState(() {
                          _currentText = text;
                        });
                      },
                      onStyleChanged: (style) {
                        setState(() {
                          _currentStyle = style;
                        });
                      },
                    ),
                  ),
                ),

                // Action buttons
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: themeProvider.surfaceColor,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Cancel button
                      Expanded(
                        child: SizedBox(
                          height: 50,
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                color: themeProvider.borderColor,
                                width: 2,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: themeProvider.textColor,
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(width: 16),

                      // Save button
                      Expanded(
                        flex: 2,
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.purple.shade600,
                                Colors.purple.shade700,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.purple.shade600.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 8,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed: _currentText.trim().isEmpty
                                ? null
                                : () {
                                    widget.onSave(
                                      _currentText.trim(),
                                      _currentStyle,
                                    );
                                    Navigator.pop(context);
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.save, color: Colors.white, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'Save Changes',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Helper function to show the enhanced edit dialog
Future<void> showEnhancedEditDialog({
  required BuildContext context,
  required String initialText,
  required Function(String, TextStyle) onSave,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) =>
        EnhancedEditDialog(initialText: initialText, onSave: onSave),
  );
}
