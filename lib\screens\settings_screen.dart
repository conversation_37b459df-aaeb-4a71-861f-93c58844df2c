// lib/screens/settings_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../utils/performance_optimizer.dart';
import '../providers/theme_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: Custom3DAppBar(title: "Settings"),
          drawer: AppDrawer(),
          body: Container(
            decoration: BoxDecoration(
              // Realistic 3D background with depth that respects theme
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: themeProvider.backgroundGradientColors,
                stops: [0.0, 0.3, 0.7, 1.0],
              ),
              // Add subtle inner shadows for depth
              boxShadow: [
                // Inner shadow effect (top-left light source)
                BoxShadow(
                  color: themeProvider.isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.shade200,
                  blurRadius: 20,
                  offset: Offset(-5, -5),
                  spreadRadius: -10,
                ),
                // Inner shadow effect (bottom-right)
                BoxShadow(
                  color: themeProvider.isDarkMode
                      ? Colors.black.withValues(alpha: 0.4)
                      : Colors.grey.shade300,
                  blurRadius: 15,
                  offset: Offset(3, 3),
                  spreadRadius: -8,
                ),
              ],
            ),
            child: ListView(
              padding: EdgeInsets.all(16),
              children: [
                // Appearance Section
                _buildSectionHeader("Appearance"),
                _buildSettingTile(
                  context: context,
                  icon: themeProvider.isDarkMode
                      ? Icons.dark_mode
                      : Icons.light_mode,
                  title: "Dark Mode",
                  subtitle: themeProvider.isDarkMode
                      ? "Dark theme enabled"
                      : "Light theme enabled",
                  value: themeProvider.isDarkMode,
                  onChanged: (value) {
                    themeProvider.setDarkMode(value);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          value ? 'Dark mode enabled' : 'Light mode enabled',
                        ),
                      ),
                    );
                  },
                ),

                SizedBox(height: 24),

                // Notifications Section
                _buildSectionHeader("Notifications"),
                _buildSettingTile(
                  context: context,
                  icon: themeProvider.pushNotifications
                      ? Icons.notifications_active
                      : Icons.notifications_off,
                  title: "Push Notifications",
                  subtitle: themeProvider.pushNotifications
                      ? "Receive notifications for new content"
                      : "Notifications are disabled",
                  value: themeProvider.pushNotifications,
                  onChanged: (value) {
                    themeProvider.setPushNotifications(value);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          value
                              ? 'Push notifications enabled'
                              : 'Push notifications disabled',
                        ),
                      ),
                    );
                  },
                ),

                SizedBox(height: 24),

                // Sound Section
                _buildSectionHeader("Sound"),
                _buildSettingTile(
                  context: context,
                  icon: themeProvider.tapSound
                      ? Icons.volume_up
                      : Icons.volume_off,
                  title: "Tap Sound",
                  subtitle: themeProvider.tapSound
                      ? "Play sound when tapping posts"
                      : "Tap sounds are disabled",
                  value: themeProvider.tapSound,
                  onChanged: (value) {
                    themeProvider.setTapSound(value);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          value ? 'Tap sounds enabled' : 'Tap sounds disabled',
                        ),
                      ),
                    );
                  },
                ),

                SizedBox(height: 32),

                // Additional Settings Section
                _buildSectionHeader("Other"),

                // Reset Settings
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: ListTile(
                    leading: Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.restore,
                        color: Colors.red.shade600,
                        size: 24,
                      ),
                    ),
                    title: Text(
                      "Reset Settings",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(
                      "Reset all settings to default values",
                      style: TextStyle(fontSize: 14),
                    ),
                    trailing: Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showResetDialog(context, themeProvider);
                    },
                  ),
                ),

                // Performance Optimization
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: ListTile(
                    leading: Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.speed,
                        color: Colors.blue.shade600,
                        size: 24,
                      ),
                    ),
                    title: Text(
                      "Clear Cache",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(
                      "Clear image cache to improve performance",
                      style: TextStyle(fontSize: 14),
                    ),
                    trailing: Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showPerformanceOptimizationDialog(context);
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Padding(
          padding: EdgeInsets.only(bottom: 12, left: 4),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: themeProvider.textColor,
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            // Enhanced 3D card with realistic depth that respects theme
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: themeProvider.cardGradientColors,
            ),
            borderRadius: BorderRadius.circular(16),
            // Realistic 3D shadow effects
            boxShadow: themeProvider.isDarkMode
                ? [
                    // Dark mode shadows
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.4),
                      blurRadius: 12,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: Offset(0, 8),
                      spreadRadius: -2,
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 30,
                      offset: Offset(0, 12),
                      spreadRadius: -8,
                    ),
                  ]
                : [
                    // Light mode shadows
                    BoxShadow(
                      color: Colors.grey.shade300,
                      blurRadius: 12,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.grey.shade200,
                      blurRadius: 20,
                      offset: Offset(0, 8),
                      spreadRadius: -2,
                    ),
                    BoxShadow(
                      color: Colors.grey.shade100,
                      blurRadius: 30,
                      offset: Offset(0, 12),
                      spreadRadius: -8,
                    ),
                    BoxShadow(
                      color: Colors.white,
                      blurRadius: 8,
                      offset: Offset(-2, -2),
                      spreadRadius: -4,
                    ),
                  ],
            // Subtle border for definition
            border: Border.all(color: themeProvider.borderColor, width: 0.5),
          ),
          child: ListTile(
            leading: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                // 3D gradient for icon background
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: themeProvider.isDarkMode
                      ? [
                          Colors.purple.shade800,
                          Colors.purple.shade700,
                          Colors.purple.shade600,
                        ]
                      : [
                          Colors.purple.shade50,
                          Colors.purple.shade100,
                          Colors.purple.shade200,
                        ],
                ),
                borderRadius: BorderRadius.circular(8),
                // 3D shadow for icon container
                boxShadow: themeProvider.isDarkMode
                    ? [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.4),
                          blurRadius: 6,
                          offset: Offset(0, 2),
                          spreadRadius: 0,
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.purple.shade200,
                          blurRadius: 6,
                          offset: Offset(0, 2),
                          spreadRadius: 0,
                        ),
                        BoxShadow(
                          color: Colors.white,
                          blurRadius: 4,
                          offset: Offset(-1, -1),
                          spreadRadius: -1,
                        ),
                      ],
              ),
              child: Icon(
                icon,
                color: themeProvider.isDarkMode
                    ? Colors.purple.shade300
                    : Colors.purple.shade600,
                size: 24,
              ),
            ),
            title: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: themeProvider.textColor,
              ),
            ),
            subtitle: Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: themeProvider.subtitleColor,
              ),
            ),
            trailing: Switch(
              value: value,
              onChanged: onChanged,
              activeColor: Colors.purple.shade600,
              activeTrackColor: Colors.purple.shade200,
              inactiveThumbColor: Colors.grey.shade400,
              inactiveTrackColor: Colors.grey.shade300,
            ),
          ),
        );
      },
    );
  }

  void _showResetDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Reset Settings"),
          content: Text(
            "Are you sure you want to reset all settings to their default values?",
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("Cancel"),
            ),
            TextButton(
              onPressed: () {
                themeProvider.resetSettings();
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Settings reset to default')),
                );
              },
              child: Text("Reset", style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showPerformanceOptimizationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Performance Optimization"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "This will clear all image caches to improve app performance and reduce memory usage.",
              ),
              SizedBox(height: 16),
              Text(
                "Performance Stats:",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              ...PerformanceOptimizer.getPerformanceStats().entries.map(
                (entry) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Text(
                    "${entry.key}: ${entry.value}",
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                navigator.pop();

                // Show loading indicator
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => AlertDialog(
                    content: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(width: 16),
                        Text("Optimizing performance..."),
                      ],
                    ),
                  ),
                );

                // Perform optimization
                await PerformanceOptimizer.clearAllCaches();

                // Close loading dialog
                navigator.pop();

                // Show success message
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('Performance optimization completed!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: Text("Optimize", style: TextStyle(color: Colors.blue)),
            ),
          ],
        );
      },
    );
  }
}
