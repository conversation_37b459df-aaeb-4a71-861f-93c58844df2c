# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Dart specific rules
-keep class com.google.dart.** { *; }

# Keep native methods
-keepclassmembers class * {
    native <methods>;
}

# Keep classes with native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep plugin classes
-keep class com.example.charmshots.charmshot1.** { *; }

# Keep permission handler
-keep class com.baseflow.permissionhandler.** { *; }

# Keep share plus
-keep class dev.fluttercommunity.plus.share.** { *; }

# Keep package info plus
-keep class dev.fluttercommunity.plus.packageinfo.** { *; }

# Keep URL launcher
-keep class io.flutter.plugins.urllauncher.** { *; }

# Keep shared preferences
-keep class io.flutter.plugins.sharedpreferences.** { *; }

# Keep path provider
-keep class io.flutter.plugins.pathprovider.** { *; }

# Keep gal (gallery access)
-keep class studio.midoridesign.gal.** { *; }

# Keep screenshot plugin
-keep class com.example.screenshot.** { *; }

# General Android optimizations
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
}
