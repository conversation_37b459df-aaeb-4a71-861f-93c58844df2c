// lib/widgets/rich_text_editor.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class RichTextEditor extends StatefulWidget {
  final String initialText;
  final Function(String) onTextChanged;
  final Function(TextStyle) onStyleChanged;

  const RichTextEditor({
    super.key,
    required this.initialText,
    required this.onTextChanged,
    required this.onStyleChanged,
  });

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  
  // Text formatting states
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderlined = false;
  
  // Font properties
  String _selectedFont = 'Roboto';
  double _fontSize = 18.0;
  Color _textColor = Colors.white;
  
  // Popular font families
  final List<String> _fontFamilies = [
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    '<PERSON>pins',
    'Nunito',
    'Source Sans Pro',
    'Raleway',
    'Ubuntu',
    'Playfair Display',
    'Merriweather',
    'Dancing Script',
    'Pacifico',
    'Lobster',
    'Comfortaa',
  ];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();
    _controller.addListener(() {
      widget.onTextChanged(_controller.text);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  TextStyle get _currentTextStyle {
    return TextStyle(
      fontFamily: _selectedFont,
      fontSize: _fontSize,
      fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
      fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
      decoration: _isUnderlined ? TextDecoration.underline : TextDecoration.none,
      color: _textColor,
      height: 1.3,
    );
  }

  void _updateStyle() {
    widget.onStyleChanged(_currentTextStyle);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          decoration: BoxDecoration(
            color: themeProvider.cardBackgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: themeProvider.borderColor),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Formatting toolbar
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: themeProvider.surfaceColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Column(
                  children: [
                    // Font family dropdown
                    Row(
                      children: [
                        Text(
                          'Font: ',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: themeProvider.textColor,
                          ),
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: themeProvider.cardBackgroundColor,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: themeProvider.borderColor),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedFont,
                                isExpanded: true,
                                style: TextStyle(color: themeProvider.textColor),
                                dropdownColor: themeProvider.cardBackgroundColor,
                                items: _fontFamilies.map((font) {
                                  return DropdownMenuItem(
                                    value: font,
                                    child: Text(
                                      font,
                                      style: TextStyle(
                                        fontFamily: font,
                                        color: themeProvider.textColor,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedFont = value;
                                    });
                                    _updateStyle();
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 8),
                    
                    // Font size slider
                    Row(
                      children: [
                        Text(
                          'Size: ',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: themeProvider.textColor,
                          ),
                        ),
                        Expanded(
                          child: Slider(
                            value: _fontSize,
                            min: 12.0,
                            max: 32.0,
                            divisions: 20,
                            activeColor: Colors.purple.shade600,
                            inactiveColor: themeProvider.borderColor,
                            onChanged: (value) {
                              setState(() {
                                _fontSize = value;
                              });
                              _updateStyle();
                            },
                          ),
                        ),
                        Text(
                          '${_fontSize.round()}',
                          style: TextStyle(color: themeProvider.textColor),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 8),
                    
                    // Formatting buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildFormatButton(
                          icon: Icons.format_bold,
                          isActive: _isBold,
                          onPressed: () {
                            setState(() {
                              _isBold = !_isBold;
                            });
                            _updateStyle();
                          },
                          themeProvider: themeProvider,
                        ),
                        _buildFormatButton(
                          icon: Icons.format_italic,
                          isActive: _isItalic,
                          onPressed: () {
                            setState(() {
                              _isItalic = !_isItalic;
                            });
                            _updateStyle();
                          },
                          themeProvider: themeProvider,
                        ),
                        _buildFormatButton(
                          icon: Icons.format_underlined,
                          isActive: _isUnderlined,
                          onPressed: () {
                            setState(() {
                              _isUnderlined = !_isUnderlined;
                            });
                            _updateStyle();
                          },
                          themeProvider: themeProvider,
                        ),
                        // Color picker button
                        _buildColorButton(themeProvider),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Text input area
              Container(
                padding: EdgeInsets.all(16),
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  maxLines: 4,
                  style: _currentTextStyle.copyWith(
                    color: themeProvider.textColor, // Override for input
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter your text...',
                    hintStyle: TextStyle(color: themeProvider.subtitleColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: themeProvider.borderColor),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: themeProvider.borderColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.purple.shade600, width: 2),
                    ),
                    filled: true,
                    fillColor: themeProvider.cardBackgroundColor,
                  ),
                ),
              ),
              
              // Preview area
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                margin: EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _controller.text.isEmpty ? 'Preview will appear here...' : _controller.text,
                  style: _currentTextStyle,
                  textAlign: TextAlign.center,
                ),
              ),
              
              SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFormatButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onPressed,
    required ThemeProvider themeProvider,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive ? Colors.purple.shade600 : themeProvider.cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? Colors.purple.shade600 : themeProvider.borderColor,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: isActive ? Colors.white : themeProvider.textColor,
        ),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildColorButton(ThemeProvider themeProvider) {
    return Container(
      decoration: BoxDecoration(
        color: themeProvider.cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: themeProvider.borderColor),
      ),
      child: IconButton(
        icon: Icon(
          Icons.color_lens,
          color: _textColor,
        ),
        onPressed: () {
          _showColorPicker(themeProvider);
        },
      ),
    );
  }

  void _showColorPicker(ThemeProvider themeProvider) {
    final colors = [
      Colors.white,
      Colors.black,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.yellow,
      Colors.orange,
      Colors.purple,
      Colors.pink,
      Colors.cyan,
      Colors.amber,
      Colors.indigo,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: themeProvider.cardBackgroundColor,
        title: Text(
          'Choose Text Color',
          style: TextStyle(color: themeProvider.textColor),
        ),
        content: Wrap(
          spacing: 8,
          runSpacing: 8,
          children: colors.map((color) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  _textColor = color;
                });
                _updateStyle();
                Navigator.pop(context);
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _textColor == color 
                        ? Colors.purple.shade600 
                        : themeProvider.borderColor,
                    width: _textColor == color ? 3 : 1,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
