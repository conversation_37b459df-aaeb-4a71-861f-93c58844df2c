// lib/screens/language_selection_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart' as drawer;
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../utils/exit_confirmation_utils.dart';

// RouteObserver for tracking navigation
final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

class LanguageSelectScreen extends StatefulWidget {
  const LanguageSelectScreen({super.key});

  @override
  State<LanguageSelectScreen> createState() => _LanguageSelectScreenState();
}

class _LanguageSelectScreenState extends State<LanguageSelectScreen>
    with TickerProviderStateMixin, RouteAware {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
    );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  // RouteAware methods
  @override
  void didPopNext() {
    // Called when returning to this screen from another screen
    // Only auto-open drawer if user explicitly came from drawer navigation
    // and it's been less than 2 seconds (to avoid unwanted behavior)
    if (drawer.shouldOpenDrawerOnReturn) {
      // Add a small delay to ensure smooth transition
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted && _scaffoldKey.currentState != null) {
          _scaffoldKey.currentState!.openDrawer();
          drawer.resetDrawerFlag();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            await ExitConfirmationUtils.handlePopInvoked(
              didPop,
              result,
              context,
            );
          },
          child: Scaffold(
            key: _scaffoldKey,
            appBar: Custom3DAppBar(
              title: 'Charm Shot',
              actions: [
                _buildAIFeatureButton(themeProvider),
              ],
            ),
            drawer: drawer.AppDrawer(),
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: themeProvider.isDarkMode
                      ? [Colors.grey.shade900, Colors.black87]
                      : [Colors.grey.shade50, Colors.white],
                ),
              ),
              child: SafeArea(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Hindi Language Button
                          _buildEnhanced3DLanguageButton(
                            context: context,
                            themeProvider: themeProvider,
                            flag: '🇮🇳',
                            language: 'हिंदी',
                            subtitle: 'Hindi',
                            gradientColors: [
                              const Color(0xFF667eea),
                              const Color(0xFF764ba2),
                              const Color(0xFF891bc9),
                            ],
                            onPressed: () =>
                                _navigateToCategories(context, 'Hindi'),
                            delay: 200,
                          ),

                          const SizedBox(height: 40),

                          // Divider with "OR" text
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: 1,
                                  color: themeProvider.isDarkMode
                                      ? Colors.white.withValues(alpha: 0.24)
                                      : Colors.grey.shade300,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                ),
                                child: Text(
                                  'OR',
                                  style: TextStyle(
                                    color: themeProvider.isDarkMode
                                        ? Colors.white.withValues(alpha: 0.54)
                                        : Colors.grey.shade500,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 1.0,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  height: 1,
                                  color: themeProvider.isDarkMode
                                      ? Colors.white.withValues(alpha: 0.24)
                                      : Colors.grey.shade300,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 40),

                          // English Language Button
                          _buildEnhanced3DLanguageButton(
                            context: context,
                            themeProvider: themeProvider,
                            flag: '🇬🇧',
                            language: 'English',
                            subtitle: 'International',
                            gradientColors: [
                              const Color(0xFF2196F3),
                              const Color(0xFF21CBF3),
                              const Color(0xFF2196F3),
                            ],
                            onPressed: () =>
                                _navigateToCategories(context, 'English'),
                            delay: 400,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ), // Close Scaffold
        ); // Close PopScope
      },
    );
  }

  Widget _buildAIFeatureButton(ThemeProvider themeProvider) {
    return Tooltip(
      message: 'coming soon',
      textStyle: const TextStyle(
        fontSize: 10,
        color: Colors.white,
      ),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: () {
              // Show tooltip on tap as well
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'coming soon',
                    style: TextStyle(fontSize: 12),
                  ),
                  duration: Duration(milliseconds: 1000),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: const Center(
              child: Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhanced3DLanguageButton({
    required BuildContext context,
    required ThemeProvider themeProvider,
    required String flag,
    required String language,
    required String subtitle,
    required List<Color> gradientColors,
    required VoidCallback onPressed,
    required int delay,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: _LanguageButton(
            themeProvider: themeProvider,
            flag: flag,
            language: language,
            subtitle: subtitle,
            gradientColors: gradientColors,
            onPressed: onPressed,
          ),
        );
      },
    );
  }

  void _navigateToCategories(BuildContext context, String language) {
    // Add haptic feedback
    HapticFeedback.mediumImpact();

    // Add navigation with smooth transition
    Navigator.pushNamed(context, '/categories', arguments: language);
  }
}

class _LanguageButton extends StatefulWidget {
  final ThemeProvider themeProvider;
  final String flag;
  final String language;
  final String subtitle;
  final List<Color> gradientColors;
  final VoidCallback onPressed;

  const _LanguageButton({
    required this.themeProvider,
    required this.flag,
    required this.language,
    required this.subtitle,
    required this.gradientColors,
    required this.onPressed,
  });

  @override
  State<_LanguageButton> createState() => _LanguageButtonState();
}

class _LanguageButtonState extends State<_LanguageButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: 320,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(28),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  widget.gradientColors[0],
                  widget.gradientColors[1],
                  widget.gradientColors[2],
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.gradientColors[1].withValues(alpha: 0.4),
                  spreadRadius: 0,
                  blurRadius: _isPressed ? 8 : 20,
                  offset: Offset(0, _isPressed ? 4 : 10),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  spreadRadius: 0,
                  blurRadius: _isPressed ? 4 : 12,
                  offset: Offset(0, _isPressed ? 2 : 6),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(28),
                onTapDown: (_) {
                  setState(() => _isPressed = true);
                  _scaleController.forward();
                  HapticFeedback.lightImpact();
                },
                onTapUp: (_) {
                  setState(() => _isPressed = false);
                  _scaleController.reverse();
                },
                onTapCancel: () {
                  setState(() => _isPressed = false);
                  _scaleController.reverse();
                },
                onTap: widget.onPressed,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 20,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Flag with glow effect
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.2),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withValues(alpha: 0.3),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          widget.flag,
                          style: const TextStyle(fontSize: 36),
                        ),
                      ),
                      const SizedBox(width: 20),

                      // Language text with enhanced styling
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.language,
                              style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 0.5,
                                shadows: [
                                  Shadow(
                                    color: Colors.black38,
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              widget.subtitle,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.8),
                                letterSpacing: 0.3,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Arrow icon
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.2),
                        ),
                        child: const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
