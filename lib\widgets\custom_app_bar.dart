// lib/widgets/custom_app_bar.dart
// Custom AppBar with realistic 3D shadow effects

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class Custom3DAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final double elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const Custom3DAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.elevation = 8.0,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final bgColor = backgroundColor ?? 
            (themeProvider.isDarkMode ? Colors.grey.shade900 : Colors.white);
        final fgColor = foregroundColor ?? 
            (themeProvider.isDarkMode ? Colors.white : Colors.black);

        return Container(
          decoration: BoxDecoration(
            color: bgColor,
            boxShadow: themeProvider.getAppBarShadow(),
          ),
          child: SafeArea(
            bottom: false,
            child: Container(
              height: kToolbarHeight,
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: Row(
                children: [
                  // Leading widget
                  if (leading != null)
                    leading!
                  else if (automaticallyImplyLeading)
                    _buildDefaultLeading(context, fgColor),
                  
                  // Title
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: fgColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  
                  // Actions
                  if (actions != null)
                    ...actions!.map((action) => 
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: action,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDefaultLeading(BuildContext context, Color color) {
    final scaffoldHasDrawer = Scaffold.maybeOf(context)?.hasDrawer ?? false;
    final canPop = Navigator.canPop(context);

    if (scaffoldHasDrawer) {
      return IconButton(
        icon: Icon(Icons.menu, color: color),
        onPressed: () => Scaffold.of(context).openDrawer(),
      );
    } else if (canPop) {
      return IconButton(
        icon: Icon(Icons.arrow_back, color: color),
        onPressed: () => Navigator.pop(context),
      );
    }
    
    return const SizedBox.shrink();
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Extension to easily create 3D AppBars
extension AppBarExtensions on AppBar {
  static Custom3DAppBar create3D({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    double elevation = 8.0,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return Custom3DAppBar(
      title: title,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      elevation: elevation,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }
}
