// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'dart:ui'; // Add this import for PointerDeviceKind
import 'package:provider/provider.dart';

import 'screens/language_selection_screen.dart';
import 'screens/category_screen.dart';
import 'screens/lines_list_screen.dart';
import 'screens/performance_debug_screen.dart';
import 'screens/favorites_screen.dart';
import 'utils/storage_permission_manager.dart';
import 'providers/theme_provider.dart';
import 'providers/favorites_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Optimize Flutter engine settings for better performance
  if (!kDebugMode) {
    // Disable debugging overlays in release mode
    debugPrint = (String? message, {int? wrapWidth}) {};
  }

  // Performance optimizations to reduce frame drops and memory usage
  // Optimize image cache to prevent memory issues - reduced for smaller app footprint
  PaintingBinding.instance.imageCache.maximumSize = 50;
  PaintingBinding.instance.imageCache.maximumSizeBytes = 25 << 20; // 25MB

  // Enable performance monitoring in debug mode
  if (kDebugMode) {
    // Monitor frame performance
    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      // This helps track frame timing issues
    });
  }

  // Enable hardware acceleration and optimize rendering
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Set preferred orientations for better performance
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FavoritesProvider()),
      ],
      child: CharmShotsApp(),
    ),
  );
}

class CharmShotsApp extends StatefulWidget {
  const CharmShotsApp({super.key});

  @override
  State<CharmShotsApp> createState() => _CharmShotsAppState();
}

class _CharmShotsAppState extends State<CharmShotsApp>
    with WidgetsBindingObserver {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Minimal initialization to avoid blocking UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeMinimal();
    });

    // Defer heavy initialization
    Future.microtask(() => _initializeHeavyTasks());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Quick initialization for essential features
  void _initializeMinimal() {
    try {
      context.read<FavoritesProvider>().initialize();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing favorites: $e');
      }
    }
  }

  // Heavy tasks that can be deferred
  void _initializeHeavyTasks() async {
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    try {
      // Initialize storage permissions after UI is ready
      StoragePermissionManager.instance.initialize();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing storage: $e');
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Optimize for app lifecycle changes
    switch (state) {
      case AppLifecycleState.paused:
        // Clear unnecessary caches when app goes to background
        _clearTemporaryCaches();
        break;
      case AppLifecycleState.resumed:
        // Minimal refresh when app comes back
        _refreshMinimal();
        break;
      default:
        break;
    }
  }

  void _clearTemporaryCaches() {
    // Clear image cache if memory is low
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  void _refreshMinimal() {
    if (mounted) {
      // Instead of rebuilding the entire tree, just clear caches
      // This is more efficient than calling setState on the root widget
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Only rebuild if there are actual changes needed
      // For now, we'll skip the setState to avoid unnecessary rebuilds
      // setState(() {}); // Commented out to prevent full tree rebuild
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'Charm Shots',
          debugShowCheckedModeBanner: false,

          // Performance optimizations
          navigatorKey: navigatorKey,
          navigatorObservers: [routeObserver],

          // Optimized theme configuration
          theme: themeProvider.currentTheme,

          // Performance-focused material app settings
          builder: (context, child) {
            return MediaQuery(
              // Disable text scaling for consistent performance
              data: MediaQuery.of(
                context,
              ).copyWith(textScaler: TextScaler.noScaling),
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: child ?? const SizedBox.shrink(),
              ),
            );
          },

          // Simplified routing for better performance
          initialRoute: '/language',
          routes: _buildOptimizedRoutes(),

          // Minimal page transitions
          onGenerateRoute: (settings) => _generateOptimizedRoute(settings),

          // Optimize material app for scrolling performance
          scrollBehavior: const CustomScrollBehavior(),
        );
      },
    );
  }

  // Optimized static routes for better performance
  Map<String, WidgetBuilder> _buildOptimizedRoutes() {
    return {
      '/language': (context) => const LanguageSelectScreen(),
      '/performance-debug': (context) => const PerformanceDebugScreen(),
      '/favorites': (context) => const FavoritesScreen(),
    };
  }

  // Generate routes with minimal transitions
  Route<dynamic>? _generateOptimizedRoute(RouteSettings settings) {
    Widget page;

    switch (settings.name) {
      case '/categories':
        // Safe argument handling
        final args = settings.arguments;
        final language = (args is String) ? args : 'English';
        page = CategoryScreen(language: language);
        break;

      case '/lines':
        final args = settings.arguments;
        if (args is Map<String, String>) {
          page = LinesListScreen(
            category: args['category'] ?? 'Bold',
            language: args['language'] ?? 'English',
          );
        } else if (args is String) {
          // Safe handling when argument is a string
          page = LinesListScreen(category: args, language: 'English');
        } else {
          // Safe fallback for any other type or null
          page = const LinesListScreen(category: 'Bold', language: 'English');
        }
        break;

      default:
        return null;
    }

    // Ultra-fast route transitions to prevent frame drops
    return PageRouteBuilder<dynamic>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(
        milliseconds: 50,
      ), // Minimal transition time
      reverseTransitionDuration: const Duration(milliseconds: 50),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Instant transition - no animation to prevent frame drops
        return child;

        // Alternative: Minimal fade if you want some animation
        // return FadeTransition(
        //   opacity: Tween<double>(
        //     begin: 0.95, // Start nearly opaque for faster transition
        //     end: 1.0,
        //   ).animate(CurvedAnimation(
        //     parent: animation,
        //     curve: Curves.easeOut,
        //   )),
        //   child: child,
        // );
      },
    );
  }
}

// Custom scroll behavior for optimized scrolling performance
class CustomScrollBehavior extends ScrollBehavior {
  const CustomScrollBehavior();

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // Enhanced smooth scrolling physics for all platforms
    // Use BouncingScrollPhysics for smoother feel across all platforms
    return const BouncingScrollPhysics(
      parent: AlwaysScrollableScrollPhysics(),
    );
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Show lightweight scrollbars on mobile for better UX
    // Only disable on very small screens where space is critical
    final mediaQuery = MediaQuery.of(context);
    final isVerySmallScreen = mediaQuery.size.width < 360;

    if ((Theme.of(context).platform == TargetPlatform.android ||
            Theme.of(context).platform == TargetPlatform.iOS) &&
        isVerySmallScreen) {
      return child; // Disable only on very small screens
    }

    // Use lightweight scrollbar for better performance
    return Scrollbar(
      controller: details.controller,
      thumbVisibility: false, // Only show when scrolling
      thickness: 4.0, // Thin scrollbar
      radius: const Radius.circular(2.0),
      child: child,
    );
  }

  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Enhanced smooth overscroll indicator
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
        return child; // iOS has no overscroll indicator
      default:
        return GlowingOverscrollIndicator(
          axisDirection: details.direction,
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
          child: child,
        );
    }
  }

  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.trackpad,
      };
}

// Global navigation service for optimized navigation
class NavigationService {
  // Use the same navigator key as the main app
  static GlobalKey<NavigatorState> get navigatorKey =>
      _CharmShotsAppState.navigatorKey;

  static BuildContext? get currentContext => navigatorKey.currentContext;

  static Future<T?> pushNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) {
    final state = navigatorKey.currentState;
    if (state == null) return Future.value(null);
    return state.pushNamed<T>(routeName, arguments: arguments);
  }

  static Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
  }) {
    final state = navigatorKey.currentState;
    if (state == null) return Future.value(null);
    return state.pushReplacementNamed<T, TO>(
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  static void pop<T extends Object?>([T? result]) {
    final state = navigatorKey.currentState;
    if (state != null) {
      state.pop<T>(result);
    }
  }

  static bool canPop() {
    final state = navigatorKey.currentState;
    return state?.canPop() ?? false;
  }
}
