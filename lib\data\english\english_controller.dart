// lib/data/english/english_controller.dart
// Controller for all English pickup lines categories

import 'bold.dart';
import 'cute.dart';
import 'romantic.dart';
import 'funny.dart';
import 'flirty.dart';
import 'clever.dart';
import 'genius.dart';
import 'dirty.dart';
import 'food.dart';
import 'nerd.dart';
import 'hookup.dart';
import 'bad.dart';

class EnglishPickupLines {
  static const Map<String, List<String>> _categoryMap = {
    'Bold': BoldEnglishLines.lines,
    'Cute': CuteEnglishLines.lines,
    'Romantic': RomanticEnglishLines.lines,
    'Funny': FunnyEnglishLines.lines,
    'Flirty': FlirtyEnglishLines.lines,
    'Clever': CleverEnglishLines.lines,
    'Genius': GeniusEnglishLines.lines,
    'Dirty': DirtyEnglishLines.lines,
    'Food': FoodEnglishLines.lines,
    'Nerd': NerdEnglishLines.lines,
    'Hookup': HookupEnglishLines.lines,
    'Bad': BadEnglishLines.lines,
  };

  // Get all available categories
  static List<String> getAvailableCategories() {
    return _categoryMap.keys.toList();
  }

  // Get lines for a specific category
  static List<String> getLinesForCategory(String category) {
    return _categoryMap[category] ?? _categoryMap['Bold'] ?? [];
  }

  // Get all lines from all categories
  static List<String> getAllLines() {
    List<String> allLines = [];
    for (String category in getAvailableCategories()) {
      allLines.addAll(getLinesForCategory(category));
    }
    return allLines;
  }

  // Get total count of lines
  static int getTotalLinesCount() {
    return getAllLines().length;
  }

  // Get count for specific category
  static int getLinesCountForCategory(String category) {
    return getLinesForCategory(category).length;
  }
}
