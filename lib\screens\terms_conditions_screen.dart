// lib/screens/terms_conditions_screen.dart
// Terms and Conditions screen for Play Store compliance

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/custom_app_bar.dart';

class TermsConditionsScreen extends StatelessWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor: Colors.white, // Always white background
          appBar: Custom3DAppBar(
            title: 'Terms and Conditions',
            elevation: 12.0, // Enhanced 3D effect
            backgroundColor: Colors.white,
            foregroundColor: Colors.black87,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white,
                  Colors.grey[50]!,
                ], // Always white gradient
              ),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white, // Always white
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: themeProvider.getCardShadow(
                    elevation: 12.0,
                  ), // Enhanced 3D shadow
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Acceptance of Terms',
                      'By downloading and using Charm Shots, you agree to be bound by these terms and conditions.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Use of the App',
                      'Charm Shots is intended for entertainment purposes only. The pickup lines are meant to be fun and lighthearted. Use them responsibly and respectfully.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Content Guidelines',
                      'All content in the app is designed to be family-friendly and appropriate. We do not tolerate harassment, inappropriate behavior, or misuse of the content.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Intellectual Property',
                      'The content, design, and functionality of Charm Shots are protected by copyright and other intellectual property laws.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Disclaimer',
                      'The app is provided "as is" without warranties. We are not responsible for any outcomes from using the pickup lines.',
                      themeProvider,
                    ),
                    _buildSection(
                      'User Responsibility',
                      'Users are responsible for using the app content appropriately and respectfully. Always respect others\' boundaries and consent.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Updates and Changes',
                      'We may update these terms from time to time. Continued use of the app constitutes acceptance of any changes.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Contact Information',
                      'For questions about these terms, please contact us through the app store.',
                      themeProvider,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Last updated: ${DateTime.now().toString().split(' ')[0]}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600], // Always grey text
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSection(
    String title,
    String content,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87, // Always dark text on white background
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: Colors.grey[700], // Always dark grey text
            ),
          ),
        ],
      ),
    );
  }
}
