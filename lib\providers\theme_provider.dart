// lib/providers/theme_provider.dart
import 'package:flutter/material.dart';
import '../utils/shadow_utils.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  bool _pushNotifications = true;
  bool _tapSound = true;

  bool get isDarkMode => _isDarkMode;
  bool get pushNotifications => _pushNotifications;
  bool get tapSound => _tapSound;

  ThemeData get currentTheme => _isDarkMode ? _darkTheme : _lightTheme;

  // Light Theme
  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primarySwatch: Colors.purple,
    primaryColor: Colors.purple.shade600,
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 8,
      shadowColor: Colors.black.withValues(alpha: 0.15),
      titleTextStyle: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    ),
    drawerTheme: DrawerThemeData(backgroundColor: Colors.white),
    cardTheme: CardThemeData(color: Colors.white),
    textTheme: TextTheme(
      bodyLarge: TextStyle(color: Colors.black87),
      bodyMedium: TextStyle(color: Colors.black87),
      titleLarge: TextStyle(color: Colors.black87),
    ),
  );

  // Dark Theme
  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primarySwatch: Colors.purple,
    primaryColor: Colors.purple.shade400,
    scaffoldBackgroundColor: Colors.grey.shade900,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.grey.shade900,
      foregroundColor: Colors.white,
      elevation: 8,
      shadowColor: Colors.black.withValues(alpha: 0.4),
      titleTextStyle: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
    drawerTheme: DrawerThemeData(backgroundColor: Colors.grey.shade800),
    cardTheme: CardThemeData(color: Colors.grey.shade800),
    textTheme: TextTheme(
      bodyLarge: TextStyle(color: Colors.white),
      bodyMedium: TextStyle(color: Colors.white),
      titleLarge: TextStyle(color: Colors.white),
    ),
  );

  void toggleDarkMode() {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }

  void setDarkMode(bool value) {
    _isDarkMode = value;
    notifyListeners();
  }

  void setPushNotifications(bool value) {
    _pushNotifications = value;
    notifyListeners();
  }

  void setTapSound(bool value) {
    _tapSound = value;
    notifyListeners();
  }

  void resetSettings() {
    _isDarkMode = false;
    _pushNotifications = true;
    _tapSound = true;
    notifyListeners();
  }

  // 3D Shadow helper methods
  List<BoxShadow> getAppBarShadow() {
    return ShadowUtils.createAppBarShadow(isDarkMode: _isDarkMode);
  }

  List<BoxShadow> getCardShadow({double elevation = 8.0}) {
    return ShadowUtils.createFloatingCardShadow(
      isDarkMode: _isDarkMode,
      elevation: elevation,
    );
  }

  // Helper methods for consistent background colors
  Color get primaryBackgroundColor =>
      _isDarkMode ? Colors.grey.shade900 : Colors.white;

  Color get cardBackgroundColor =>
      _isDarkMode ? Colors.grey.shade800 : Colors.white;

  Color get surfaceColor =>
      _isDarkMode ? Colors.grey.shade700 : Colors.grey.shade50;

  Color get textColor => _isDarkMode ? Colors.white : Colors.black87;

  Color get subtitleColor =>
      _isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600;

  Color get borderColor =>
      _isDarkMode ? Colors.grey.shade600 : Colors.grey.shade100;

  // Get background gradient colors for 3D effect
  List<Color> get backgroundGradientColors => _isDarkMode
      ? [
          Colors.grey.shade900,
          Colors.grey.shade800,
          Colors.grey.shade700,
          Colors.grey.shade900,
        ]
      : [Colors.white, Colors.white, Colors.grey.shade50, Colors.white];

  // Get card gradient colors for 3D effect
  List<Color> get cardGradientColors => _isDarkMode
      ? [Colors.grey.shade800, Colors.grey.shade700, Colors.grey.shade800]
      : [Colors.white, Colors.grey.shade50, Colors.white];

  List<BoxShadow> getButtonShadow({bool isPressed = false}) {
    return ShadowUtils.createButtonShadow(
      isDarkMode: _isDarkMode,
      isPressed: isPressed,
    );
  }

  List<BoxShadow> getDrawerShadow() {
    return ShadowUtils.createDrawerShadow(isDarkMode: _isDarkMode);
  }

  List<BoxShadow> get3DShadow({double elevation = 4.0}) {
    return ShadowUtils.create3DShadow(
      elevation: elevation,
      isDarkMode: _isDarkMode,
    );
  }

  // Premium gradient colors for enhanced UI
  List<Color> get premiumGradientPrimary => _isDarkMode
      ? [
          const Color(0xFF614385), // Kashmir gradient - premium purple
          const Color(0xFF516395), // Kashmir gradient - premium blue
        ]
      : [
          const Color(0xFF43cea2), // Endless River gradient - premium teal
          const Color(0xFF185a9d), // Endless River gradient - premium blue
        ];

  List<Color> get premiumGradientSecondary => _isDarkMode
      ? [
          const Color(0xFF667eea), // Cosmic Fusion gradient - premium blue
          const Color(0xFF764ba2), // Cosmic Fusion gradient - premium purple
        ]
      : [
          const Color(0xFF667eea), // Cosmic Fusion gradient - premium blue
          const Color(0xFF764ba2), // Cosmic Fusion gradient - premium purple
        ];

  List<Color> get premiumGradientAccent => _isDarkMode
      ? [
          const Color(0xFF8360c3), // Sunset gradient - premium purple
          const Color(0xFF2ebf91), // Sunset gradient - premium teal
        ]
      : [
          const Color(0xFF8360c3), // Sunset gradient - premium purple
          const Color(0xFF2ebf91), // Sunset gradient - premium teal
        ];

  // Premium card gradient with subtle transparency
  List<Color> get premiumCardGradient => _isDarkMode
      ? [
          const Color(0xFF614385).withValues(alpha: 0.1),
          const Color(0xFF516395).withValues(alpha: 0.05),
          Colors.grey.shade800,
        ]
      : [
          const Color(0xFF43cea2).withValues(alpha: 0.05),
          const Color(0xFF185a9d).withValues(alpha: 0.03),
          Colors.white,
        ];
}
