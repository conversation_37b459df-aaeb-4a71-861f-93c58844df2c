// lib/utils/animation_utils.dart
// Utility class for smooth animations throughout the app

import 'package:flutter/material.dart';

class AnimationUtils {
  // Standard animation durations for consistency
  static const Duration fastDuration = Duration(milliseconds: 150);
  static const Duration normalDuration = Duration(milliseconds: 200);
  static const Duration slowDuration = Duration(milliseconds: 300);
  
  // Standard animation curves for smooth motion
  static const Curve smoothCurve = Curves.easeInOutCubic;
  static const Curve fastCurve = Curves.easeOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;
  
  /// Creates a smooth fade transition
  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
    Duration duration = normalDuration,
    Curve curve = smoothCurve,
  }) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: animation,
        curve: curve,
      ),
      child: child,
    );
  }
  
  /// Creates a smooth slide transition
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
    Curve curve = smoothCurve,
  }) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: child,
    );
  }
  
  /// Creates a smooth scale transition
  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = smoothCurve,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: child,
    );
  }
  
  /// Creates a combined fade and slide transition
  static Widget fadeSlideTransition({
    required Widget child,
    required Animation<double> animation,
    Offset begin = const Offset(0.0, 0.3),
    Offset end = Offset.zero,
    Curve curve = smoothCurve,
  }) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: curve,
        ),
        child: child,
      ),
    );
  }
  
  /// Creates a smooth rotation transition
  static Widget rotationTransition({
    required Widget child,
    required Animation<double> animation,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = smoothCurve,
  }) {
    return RotationTransition(
      turns: Tween<double>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: child,
    );
  }
  
  /// Creates a smooth size transition
  static Widget sizeTransition({
    required Widget child,
    required Animation<double> animation,
    Axis axis = Axis.vertical,
    double axisAlignment = 0.0,
    Curve curve = smoothCurve,
  }) {
    return SizeTransition(
      sizeFactor: CurvedAnimation(
        parent: animation,
        curve: curve,
      ),
      axis: axis,
      axisAlignment: axisAlignment,
      child: child,
    );
  }
  
  /// Creates a smooth list item animation
  static Widget listItemAnimation({
    required Widget child,
    required Animation<double> animation,
    int index = 0,
    Duration delay = const Duration(milliseconds: 50),
  }) {
    final delayedAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: Interval(
        (index * 0.1).clamp(0.0, 0.8),
        1.0,
        curve: smoothCurve,
      ),
    ));
    
    return fadeSlideTransition(
      child: child,
      animation: delayedAnimation,
      begin: const Offset(0.0, 0.5),
    );
  }
  
  /// Creates a smooth card animation with scale and fade
  static Widget cardAnimation({
    required Widget child,
    required Animation<double> animation,
    Curve curve = smoothCurve,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: curve,
        ),
        child: child,
      ),
    );
  }
  
  /// Creates a smooth button press animation
  static Widget buttonPressAnimation({
    required Widget child,
    required bool isPressed,
    Duration duration = const Duration(milliseconds: 100),
  }) {
    return AnimatedScale(
      scale: isPressed ? 0.95 : 1.0,
      duration: duration,
      curve: fastCurve,
      child: child,
    );
  }
  
  /// Creates a smooth container animation
  static Widget animatedContainer({
    required Widget child,
    required bool condition,
    Duration duration = normalDuration,
    Curve curve = smoothCurve,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Decoration? decoration,
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: curve,
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: decoration,
      child: child,
    );
  }
  
  /// Creates a smooth opacity animation
  static Widget animatedOpacity({
    required Widget child,
    required bool visible,
    Duration duration = normalDuration,
    Curve curve = smoothCurve,
  }) {
    return AnimatedOpacity(
      opacity: visible ? 1.0 : 0.0,
      duration: duration,
      curve: curve,
      child: child,
    );
  }
  
  /// Creates a smooth positioned animation
  static Widget animatedPositioned({
    required Widget child,
    required double left,
    required double top,
    double? width,
    double? height,
    Duration duration = normalDuration,
    Curve curve = smoothCurve,
  }) {
    return AnimatedPositioned(
      left: left,
      top: top,
      width: width,
      height: height,
      duration: duration,
      curve: curve,
      child: child,
    );
  }
}

/// Extension for easy animation access on widgets
extension WidgetAnimationExtensions on Widget {
  Widget fadeIn({
    required Animation<double> animation,
    Curve curve = AnimationUtils.smoothCurve,
  }) {
    return AnimationUtils.fadeTransition(
      child: this,
      animation: animation,
      curve: curve,
    );
  }
  
  Widget slideIn({
    required Animation<double> animation,
    Offset begin = const Offset(1.0, 0.0),
    Curve curve = AnimationUtils.smoothCurve,
  }) {
    return AnimationUtils.slideTransition(
      child: this,
      animation: animation,
      begin: begin,
      curve: curve,
    );
  }
  
  Widget scaleIn({
    required Animation<double> animation,
    double begin = 0.0,
    Curve curve = AnimationUtils.smoothCurve,
  }) {
    return AnimationUtils.scaleTransition(
      child: this,
      animation: animation,
      begin: begin,
      curve: curve,
    );
  }
}
