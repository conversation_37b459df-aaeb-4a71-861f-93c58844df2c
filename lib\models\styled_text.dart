// lib/models/styled_text.dart
import 'package:flutter/material.dart';

class StyledText {
  final String text;
  final String fontFamily;
  final double fontSize;
  final bool isBold;
  final bool isItalic;
  final bool isUnderlined;
  final Color textColor;

  const StyledText({
    required this.text,
    this.fontFamily = 'Roboto',
    this.fontSize = 18.0,
    this.isBold = true,
    this.isItalic = false,
    this.isUnderlined = false,
    this.textColor = Colors.white,
  });

  // Convert to TextStyle
  TextStyle toTextStyle() {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
      fontStyle: isItalic ? FontStyle.italic : FontStyle.normal,
      decoration: isUnderlined ? TextDecoration.underline : TextDecoration.none,
      color: textColor,
      height: 1.3,
      shadows: [
        Shadow(
          color: Colors.black.withValues(alpha: 0.5),
          offset: Offset(1, 1),
          blurRadius: 2,
        ),
      ],
    );
  }

  // Create from TextStyle
  static StyledText fromTextStyle(String text, TextStyle style) {
    return StyledText(
      text: text,
      fontFamily: style.fontFamily ?? 'Roboto',
      fontSize: style.fontSize ?? 18.0,
      isBold: style.fontWeight == FontWeight.bold,
      isItalic: style.fontStyle == FontStyle.italic,
      isUnderlined: style.decoration == TextDecoration.underline,
      textColor: style.color ?? Colors.white,
    );
  }

  // Create a copy with updated values
  StyledText copyWith({
    String? text,
    String? fontFamily,
    double? fontSize,
    bool? isBold,
    bool? isItalic,
    bool? isUnderlined,
    Color? textColor,
  }) {
    return StyledText(
      text: text ?? this.text,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      isBold: isBold ?? this.isBold,
      isItalic: isItalic ?? this.isItalic,
      isUnderlined: isUnderlined ?? this.isUnderlined,
      textColor: textColor ?? this.textColor,
    );
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'fontFamily': fontFamily,
      'fontSize': fontSize,
      'isBold': isBold,
      'isItalic': isItalic,
      'isUnderlined': isUnderlined,
      'textColorRed': (textColor.r * 255.0).round() & 0xff,
      'textColorGreen': (textColor.g * 255.0).round() & 0xff,
      'textColorBlue': (textColor.b * 255.0).round() & 0xff,
      'textColorAlpha': (textColor.a * 255.0).round() & 0xff,
    };
  }

  // Create from JSON
  static StyledText fromJson(Map<String, dynamic> json) {
    Color color = Colors.white;
    if (json.containsKey('textColorRed')) {
      color = Color.fromARGB(
        json['textColorAlpha'] ?? 255,
        json['textColorRed'] ?? 255,
        json['textColorGreen'] ?? 255,
        json['textColorBlue'] ?? 255,
      );
    } else if (json.containsKey('textColor')) {
      // Fallback for old format
      color = Color(json['textColor'] ?? 0xFFFFFFFF);
    }

    return StyledText(
      text: json['text'] ?? '',
      fontFamily: json['fontFamily'] ?? 'Roboto',
      fontSize: (json['fontSize'] ?? 18.0).toDouble(),
      isBold: json['isBold'] ?? true,
      isItalic: json['isItalic'] ?? false,
      isUnderlined: json['isUnderlined'] ?? false,
      textColor: color,
    );
  }

  @override
  String toString() {
    return 'StyledText(text: $text, fontFamily: $fontFamily, fontSize: $fontSize, isBold: $isBold, isItalic: $isItalic, isUnderlined: $isUnderlined, textColor: $textColor)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StyledText &&
        other.text == text &&
        other.fontFamily == fontFamily &&
        other.fontSize == fontSize &&
        other.isBold == isBold &&
        other.isItalic == isItalic &&
        other.isUnderlined == isUnderlined &&
        other.textColor == textColor;
  }

  @override
  int get hashCode {
    return Object.hash(
      text,
      fontFamily,
      fontSize,
      isBold,
      isItalic,
      isUnderlined,
      textColor,
    );
  }
}
