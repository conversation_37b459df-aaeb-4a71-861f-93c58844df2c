// lib/utils/performance_optimizer.dart
// Performance optimization utilities to reduce frame drops and widget rebuilds

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'image_cache_manager.dart';

class PerformanceOptimizer {
  static final PerformanceOptimizer _instance =
      PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  static bool _isOptimizationEnabled = true;
  static int _frameDropCount = 0;
  static DateTime? _lastOptimization;

  /// Enable/disable performance optimizations
  static void setOptimizationEnabled(bool enabled) {
    _isOptimizationEnabled = enabled;
    if (kDebugMode) {
      debugPrint(
        '🚀 Performance optimization ${enabled ? 'enabled' : 'disabled'}',
      );
    }
  }

  /// Clear all caches to improve performance - addresses performance warning
  static Future<void> clearAllCaches() async {
    if (!_isOptimizationEnabled) return;

    if (kDebugMode) {
      debugPrint('🧹 Starting comprehensive cache cleanup...');
    }

    try {
      // Clear image cache
      OptimizedImageCache().clearCache();

      // Clear Flutter's image cache
      imageCache.clear();
      imageCache.clearLiveImages();

      // Clear platform channel cache (if needed)
      // Note: Platform channel cache clearing is handled automatically by Flutter

      // Force garbage collection
      if (kDebugMode) {
        debugPrint('🗑️ Forcing garbage collection...');
      }

      _lastOptimization = DateTime.now();

      if (kDebugMode) {
        debugPrint('✅ Cache cleanup completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error during cache cleanup: $e');
      }
    }
  }

  /// Optimize widget rebuilds by using const constructors and keys
  static Widget optimizedBuilder({
    required Widget Function() builder,
    String? debugLabel,
  }) {
    if (!_isOptimizationEnabled) {
      return builder();
    }

    return _OptimizedWidget(builder: builder, debugLabel: debugLabel);
  }

  /// Reduce animation complexity to prevent frame drops
  static Duration getOptimizedAnimationDuration(Duration original) {
    if (!_isOptimizationEnabled) return original;

    // Reduce animation duration by 30% to improve performance
    return Duration(milliseconds: (original.inMilliseconds * 0.7).round());
  }

  /// Get optimized curve for animations
  static Curve getOptimizedCurve() {
    if (!_isOptimizationEnabled) return Curves.easeInOut;

    // Use faster, simpler curves
    return Curves.linear;
  }

  /// Monitor frame drops and auto-optimize if needed
  static void reportFrameDrop() {
    _frameDropCount++;

    if (kDebugMode) {
      debugPrint('⚠️ Frame drop detected (total: $_frameDropCount)');
    }

    // Auto-optimize if too many frame drops
    if (_frameDropCount > 5 &&
        (_lastOptimization == null ||
            DateTime.now().difference(_lastOptimization!).inSeconds > 30)) {
      if (kDebugMode) {
        debugPrint('🔧 Auto-optimizing due to frame drops...');
      }

      clearAllCaches();
      _frameDropCount = 0;
    }
  }

  /// Get performance statistics
  static Map<String, dynamic> getPerformanceStats() {
    return {
      'optimizationEnabled': _isOptimizationEnabled,
      'frameDropCount': _frameDropCount,
      'lastOptimization': _lastOptimization?.toIso8601String(),
      'imageCacheSize': OptimizedImageCache().cacheSize,
      'flutterImageCacheSize': imageCache.currentSize,
      'flutterImageCacheLimit': imageCache.maximumSize,
    };
  }

  /// Reset performance counters
  static void resetCounters() {
    _frameDropCount = 0;
    _lastOptimization = null;
    if (kDebugMode) {
      debugPrint('🔄 Performance counters reset');
    }
  }
}

/// Optimized widget wrapper to reduce unnecessary rebuilds
class _OptimizedWidget extends StatefulWidget {
  final Widget Function() builder;
  final String? debugLabel;

  const _OptimizedWidget({required this.builder, this.debugLabel});

  @override
  State<_OptimizedWidget> createState() => _OptimizedWidgetState();
}

class _OptimizedWidgetState extends State<_OptimizedWidget>
    with AutomaticKeepAliveClientMixin {
  Widget? _cachedWidget;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Cache the widget to prevent unnecessary rebuilds
    _cachedWidget ??= widget.builder();

    return _cachedWidget!;
  }

  @override
  void didUpdateWidget(_OptimizedWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Only rebuild if the builder function changed
    if (oldWidget.builder != widget.builder) {
      _cachedWidget = null;
    }
  }
}

/// Mixin for widgets to implement performance optimizations
mixin PerformanceOptimizedMixin<T extends StatefulWidget> on State<T> {
  /// Override to implement custom optimization logic
  bool shouldOptimize() => true;

  /// Optimized setState that reduces unnecessary rebuilds
  void optimizedSetState(VoidCallback fn) {
    if (!shouldOptimize()) {
      setState(fn);
      return;
    }

    // Only call setState if the widget is still mounted and visible
    if (mounted) {
      setState(fn);
    }
  }

  /// Report frame drops for monitoring
  void reportFrameDropIfNeeded() {
    if (shouldOptimize()) {
      PerformanceOptimizer.reportFrameDrop();
    }
  }
}

/// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool showOverlay;

  const PerformanceMonitor({
    super.key,
    required this.child,
    this.showOverlay = false,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  @override
  Widget build(BuildContext context) {
    if (!widget.showOverlay || !kDebugMode) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 100,
          right: 16,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black87,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Performance Stats',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                ...PerformanceOptimizer.getPerformanceStats().entries.map(
                  (entry) => Text(
                    '${entry.key}: ${entry.value}',
                    style: TextStyle(color: Colors.white70, fontSize: 10),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
