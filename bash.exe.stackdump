Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB3B240000 ntdll.dll
7FFB396F0000 KERNEL32.DLL
7FFB387E0000 KERNELBASE.dll
7FFB3AE20000 USER32.dll
7FFB38670000 win32u.dll
7FFB39200000 GDI32.dll
7FFB386A0000 gdi32full.dll
7FFB38BD0000 msvcp_win.dll
7FFB38E90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB39D00000 advapi32.dll
7FFB39360000 msvcrt.dll
7FFB3AD70000 sechost.dll
7FFB3A4A0000 RPCRT4.dll
7FFB379C0000 CRYPTBASE.DLL
7FFB38450000 bcryptPrimitives.dll
7FFB3A5C0000 IMM32.DLL
