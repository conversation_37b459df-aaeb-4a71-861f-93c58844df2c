// lib/data/categories_data.dart
import 'package:flutter/material.dart';
import '../providers/theme_provider.dart';

class CategoriesData {
  // Premium gradient colors for categories with enhanced visual appeal
  static List<List<Color>> getPremiumColors(ThemeProvider themeProvider) {
    return [
      themeProvider.premiumGradientPrimary,
      themeProvider.premiumGradientSecondary,
      themeProvider.premiumGradientAccent,
      [const Color(0xFF667eea), const Color(0xFF764ba2)], // Cosmic Fusion
      [const Color(0xFF43cea2), const Color(0xFF185a9d)], // Endless River
      [const Color(0xFF8360c3), const Color(0xFF2ebf91)], // Mystical Sunset
      [const Color(0xFFf093fb), const Color(0xFFf5576c)], // Sweet Dreams
      [const Color(0xFF4facfe), const Color(0xFF00f2fe)], // Azure Skies
      [const Color(0xFFa8edea), const Color(0xFFfed6e3)], // Soft Pastel
      [const Color(0xFFffecd2), const Color(0xFFfcb69f)], // Golden Hour
      [const Color(0xFFd299c2), const Color(0xFFfef9d7)], // Rose Dreams
      [const Color(0xFF89f7fe), const Color(0xFF66a6ff)], // Ocean Depths
      [const Color(0xFFff9a9e), const Color(0xFFfecfef)], // Blush Pink
      [const Color(0xFF96fbc4), const Color(0xFFf9f586)], // Fresh Mint
    ];
  }

  // Category definitions with their properties
  static List<Map<String, dynamic>> getCategoryDefinitions() {
    return [
      {
        'name': 'Romantic',
        'iconAsset': 'assets/icons/romantic.svg',
        'colorIndex': 8,
      },
      {
        'name': 'Flirty',
        'iconAsset': 'assets/icons/flirty.svg',
        'colorIndex': 12,
      },
      {
        'name': 'Cute',
        'iconAsset': 'assets/icons/cute.svg',
        'colorIndex': 2,
      },
      {
        'name': 'Funny',
        'iconAsset': 'assets/icons/funny.svg',
        'colorIndex': 13,
      },
      {
        'name': 'Bad',
        'icon': Icons.thumb_down_rounded,
        'colorIndex': 1,
      },
      {
        'name': 'Clever',
        'iconAsset': 'assets/icons/cleaver.svg',
        'colorIndex': 3,
      },
      {
        'name': 'Genius',
        'iconAsset': 'assets/icons/genius.svg',
        'colorIndex': 4,
      },
      {
        'name': 'Hookup',
        'iconAsset': 'assets/icons/hookup.svg',
        'colorIndex': 7,
      },
      {
        'name': 'Dirty',
        'iconAsset': 'assets/icons/dirty.svg',
        'colorIndex': 5,
      },
      {
        'name': 'Bold',
        'iconAsset': 'assets/icons/bold.svg',
        'colorIndex': 0,
      },
      {
        'name': 'Nerd',
        'iconAsset': 'assets/icons/nerd.svg',
        'colorIndex': 10,
      },
      {
        'name': 'Food',
        'iconAsset': 'assets/icons/food.svg',
        'colorIndex': 11,
      },
    ];
  }

  // Get categories with premium colors applied
  static List<Map<String, dynamic>> getCategoriesWithPremiumColors(
    ThemeProvider themeProvider,
  ) {
    final premiumColors = getPremiumColors(themeProvider);
    final categoryDefinitions = getCategoryDefinitions();

    return categoryDefinitions.map((category) {
      final colorIndex = category['colorIndex'] as int;
      return {
        'name': category['name'],
        'icon': category['icon'],
        'iconAsset': category['iconAsset'],
        'gradientColors': premiumColors[colorIndex],
        'shadowColor': premiumColors[colorIndex][0].withValues(alpha: 0.3),
      };
    }).toList();
  }

  // Helper method to add new categories easily
  static void addCategory({
    required String name,
    IconData? icon,
    String? iconAsset,
    required int colorIndex,
  }) {
    // This method can be used to dynamically add categories in the future
    // For now, it's a placeholder for extensibility
  }

  // Helper method to get category by name
  static Map<String, dynamic>? getCategoryByName(
    String name,
    ThemeProvider themeProvider,
  ) {
    final categories = getCategoriesWithPremiumColors(themeProvider);
    try {
      return categories.firstWhere(
        (category) => category['name'] == name,
      );
    } catch (e) {
      return null;
    }
  }

  // Get total number of categories
  static int getCategoryCount() {
    return getCategoryDefinitions().length;
  }
}
