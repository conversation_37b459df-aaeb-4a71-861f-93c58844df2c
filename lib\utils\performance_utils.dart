// lib/utils/performance_utils.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'image_cache_manager.dart';

/// Performance monitoring utilities for debugging frame drops and memory issues
class PerformanceUtils {
  static final PerformanceUtils _instance = PerformanceUtils._internal();
  factory PerformanceUtils() => _instance;
  PerformanceUtils._internal();

  static bool _isMonitoring = false;
  static int _frameCount = 0;
  static int _droppedFrames = 0;
  static DateTime? _lastFrameTime;

  /// Start monitoring frame performance
  static void startFrameMonitoring() {
    if (_isMonitoring || !kDebugMode) return;

    _isMonitoring = true;
    _frameCount = 0;
    _droppedFrames = 0;
    _lastFrameTime = DateTime.now();

    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      _frameCount++;
      final now = DateTime.now();

      if (_lastFrameTime != null) {
        final frameDuration = now.difference(_lastFrameTime!);
        // Consider frame dropped if it takes more than 50ms (very significant drop)
        // Further reduced sensitivity to avoid noise
        if (frameDuration.inMilliseconds > 50) {
          _droppedFrames++;
          // Only log extremely severe frame drops to reduce console noise
          if (frameDuration.inMilliseconds > 500) {
            debugPrint(
              '! Severe frame drop: ${frameDuration.inMilliseconds}ms',
            );
          }
        }
      }

      _lastFrameTime = now;

      // Log performance stats every 300 frames (5 seconds at 60fps)
      if (_frameCount % 300 == 0) {
        final dropRate = (_droppedFrames / _frameCount * 100).toStringAsFixed(
          1,
        );
        debugPrint(
          '📊 Performance: $_frameCount frames, $_droppedFrames drops ($dropRate%)',
        );
      }
    });

    debugPrint('🚀 Frame monitoring started');
  }

  /// Stop monitoring frame performance
  static void stopFrameMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    final dropRate = _frameCount > 0
        ? (_droppedFrames / _frameCount * 100).toStringAsFixed(1)
        : '0.0';
    debugPrint(
      '🏁 Frame monitoring stopped: $_frameCount frames, $_droppedFrames drops ($dropRate%)',
    );
  }

  /// Log memory usage information with more details
  static void logMemoryUsage(String context) {
    if (!kDebugMode) return;

    // Log basic memory information
    debugPrint('💾 Memory check at $context');
    debugPrint(
      '   - Image cache size: ${OptimizedImageCache().cacheSize} images',
    );

    // In debug mode, suggest garbage collection if memory seems high
    if (_droppedFrames > 10) {
      debugPrint(
        '   ⚠️ High frame drops detected - consider reducing memory usage',
      );
    }
  }

  /// Monitor widget rebuild frequency
  static final Map<String, int> _widgetRebuildCounts = {};
  static final Map<String, DateTime> _lastRebuildTimes = {};

  static void trackWidgetRebuild(String widgetName) {
    if (!kDebugMode) return;

    final now = DateTime.now();
    _widgetRebuildCounts[widgetName] =
        (_widgetRebuildCounts[widgetName] ?? 0) + 1;

    final lastRebuild = _lastRebuildTimes[widgetName];
    if (lastRebuild != null) {
      final timeSinceLastRebuild = now.difference(lastRebuild);
      if (timeSinceLastRebuild.inMilliseconds < 16) {
        debugPrint(
          '⚠️ Rapid rebuild detected for $widgetName (${timeSinceLastRebuild.inMilliseconds}ms)',
        );
      }
    }

    _lastRebuildTimes[widgetName] = now;

    // Log rebuild stats every 50 rebuilds
    final count = _widgetRebuildCounts[widgetName]!;
    if (count % 50 == 0) {
      debugPrint('🔄 $widgetName has rebuilt $count times');
    }
  }

  /// Measure execution time of a function
  static Future<T> measureAsync<T>(
    String name,
    Future<T> Function() function,
  ) async {
    if (!kDebugMode) return await function();

    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      debugPrint('⏱️ $name took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ $name failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Measure execution time of a synchronous function
  static T measure<T>(String name, T Function() function) {
    if (!kDebugMode) return function();

    final stopwatch = Stopwatch()..start();
    try {
      final result = function();
      stopwatch.stop();
      debugPrint('⏱️ $name took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ $name failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Create a performance-optimized ListView.builder with enhanced smooth scrolling
  static Widget createOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    double? itemExtent,
    double? cacheExtent,
    ScrollController? controller,
  }) {
    return ListView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(child: itemBuilder(context, index));
      },
      padding: padding,
      // Enhanced smooth scrolling physics
      physics: physics ??
          const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
      itemExtent: itemExtent,
      cacheExtent: cacheExtent ?? 400, // Increased cache for smoother scrolling
      addAutomaticKeepAlives: false, // Disable to save memory
      addRepaintBoundaries: true, // Enable for better performance
      addSemanticIndexes: false, // Disable for better performance
    );
  }

  /// Create a performance-optimized CustomScrollView with enhanced smooth scrolling
  static Widget createOptimizedCustomScrollView({
    required List<Widget> slivers,
    ScrollController? controller,
    ScrollPhysics? physics,
    double? cacheExtent,
  }) {
    return CustomScrollView(
      controller: controller,
      // Enhanced smooth scrolling physics
      physics: physics ??
          const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
      cacheExtent:
          cacheExtent ?? 1500, // Increased cache for smoother scrolling
      slivers: slivers,
    );
  }

  /// Widget that logs its build performance
  static Widget buildTimeLogger({required String name, required Widget child}) {
    if (!kDebugMode) return child;

    return Builder(
      builder: (context) {
        trackWidgetRebuild(name);
        return measure('Building $name', () => child);
      },
    );
  }

  /// Performance monitoring overlay widget for debug mode
  static Widget performanceOverlay({required Widget child}) {
    if (!kDebugMode) return child;

    return Stack(
      children: [
        child,
        Positioned(
          top: 100,
          right: 10,
          child: Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Performance Monitor',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                SizedBox(height: 4),
                StreamBuilder<void>(
                  stream: Stream.periodic(Duration(seconds: 1)),
                  builder: (context, snapshot) {
                    final dropRate = _frameCount > 0
                        ? (_droppedFrames / _frameCount * 100).toStringAsFixed(
                            1,
                          )
                        : '0.0';
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Frames: $_frameCount',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                        Text(
                          'Drops: $_droppedFrames ($dropRate%)',
                          style: TextStyle(
                            color:
                                _droppedFrames > 5 ? Colors.red : Colors.green,
                            fontSize: 10,
                          ),
                        ),
                        Text(
                          'Cache: ${OptimizedImageCache().cacheSize}',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Reset all performance counters
  static void resetCounters() {
    _frameCount = 0;
    _droppedFrames = 0;
    _widgetRebuildCounts.clear();
    _lastRebuildTimes.clear();
    debugPrint('🔄 Performance counters reset');
  }

  /// Generate performance report
  static String generatePerformanceReport() {
    final dropRate = _frameCount > 0
        ? (_droppedFrames / _frameCount * 100).toStringAsFixed(1)
        : '0.0';

    final buffer = StringBuffer();
    buffer.writeln('📊 Performance Report');
    buffer.writeln('==================');
    buffer.writeln('Total Frames: $_frameCount');
    buffer.writeln('Dropped Frames: $_droppedFrames ($dropRate%)');
    buffer.writeln('Image Cache Size: ${OptimizedImageCache().cacheSize}');
    buffer.writeln('');
    buffer.writeln('Widget Rebuild Counts:');

    _widgetRebuildCounts.forEach((widget, count) {
      buffer.writeln('  $widget: $count rebuilds');
    });

    return buffer.toString();
  }
}

/// Extension to add performance monitoring to any widget
extension PerformanceWidget on Widget {
  Widget withPerformanceLogging(String name) {
    return PerformanceUtils.buildTimeLogger(name: name, child: this);
  }
}
