// lib/data/hindi/hindi_controller.dart
// Controller for all Hindi pickup lines categories

import 'bold.dart';
import 'cute.dart';
import 'romantic.dart';
import 'funny.dart';
import 'flirty.dart';
import 'clever.dart';
import 'genius.dart';
import 'dirty.dart';
import 'food.dart';
import 'nerd.dart';
import 'hookup.dart';
import 'bad.dart';

class HindiPickupLines {
  static const Map<String, List<String>> _categoryMap = {
    'Bold': BoldHindiLines.lines,
    'Cute': CuteHindiLines.lines,
    'Romantic': RomanticHindiLines.lines,
    'Funny': FunnyHindiLines.lines,
    'Flirty': FlirtyHindiLines.lines,
    'Clever': CleverHindiLines.lines,
    'Genius': GeniusHindiLines.lines,
    'Dirty': DirtyHindiLines.lines,
    'Food': FoodHindiLines.lines,
    'Nerd': NerdHindiLines.lines,
    'Hookup': HookupHindiLines.lines,
    'Bad': BadHindiLines.lines,
  };

  // Get all available categories
  static List<String> getAvailableCategories() {
    return _categoryMap.keys.toList();
  }

  // Get lines for a specific category
  static List<String> getLinesForCategory(String category) {
    return _categoryMap[category] ?? _categoryMap['Bold'] ?? [];
  }

  // Get all lines from all categories
  static List<String> getAllLines() {
    List<String> allLines = [];
    for (String category in getAvailableCategories()) {
      allLines.addAll(getLinesForCategory(category));
    }
    return allLines;
  }

  // Get total count of lines
  static int getTotalLinesCount() {
    return getAllLines().length;
  }

  // Get count for specific category
  static int getLinesCountForCategory(String category) {
    return getLinesForCategory(category).length;
  }
}
